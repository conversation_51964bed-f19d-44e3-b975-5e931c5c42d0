/**
 * Custom Field Value Converter
 *
 * Provides bidirectional value conversion utilities for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Handles intelligent type
 * mapping, value transformation, and graceful error handling for all supported
 * field types with comprehensive logging and request ID correlation.
 *
 * **Key Conversion Rules:**
 * - <PERSON> boolean ↔ AP RADIO (Yes/No options)
 * - <PERSON> select (multiple) ↔ AP MULTIPLE_OPTIONS (comma-separated)
 * - <PERSON> select (single) ↔ AP RADIO (option values)
 * - CC text/textarea/email/telephone ↔ AP TEXT
 * - Graceful fallbacks for unmappable types
 *
 * @fileoverview Bidirectional custom field value conversion utilities
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logWarn, logError, logInfo, logFieldConversionFailure } from "@/utils/logger";
import type {
	ValueConversionContext,
	ValueConversionResult
} from "./types";

/**
 * Convert custom field value between platforms
 *
 * Main entry point for bidirectional custom field value conversion.
 * Automatically determines conversion direction and applies appropriate
 * transformation logic based on field types and platform context.
 *
 * @param value - Source value to convert
 * @param context - Conversion context with field definitions and metadata
 * @returns Promise resolving to conversion result with success status and converted value
 *
 * @example
 * ```typescript
 * const context: ValueConversionContext = {
 *   sourceField: apRadioField,
 *   targetField: ccBooleanField,
 *   sourcePlatform: "ap",
 *   targetPlatform: "cc",
 *   requestId: "req-123"
 * };
 *
 * const result = await convertFieldValue("Yes", context);
 * if (result.success) {
 *   console.log("Converted value:", result.convertedValue); // true
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function convertFieldValue(
	value: string | number | boolean | string[] | null,
	context: ValueConversionContext,
): Promise<ValueConversionResult> {
	const { sourceField, targetField, sourcePlatform, targetPlatform, requestId } = context;

	// Handle null/undefined values
	if (value === null || value === undefined) {
		logDebug("Converting null/undefined value", {
			requestId,
			sourceFieldId: getFieldId(sourceField),
			targetFieldId: getFieldId(targetField),
		});
		return { success: true, convertedValue: null };
	}

	try {
		// Determine conversion direction and apply appropriate logic
		if (sourcePlatform === "ap" && targetPlatform === "cc") {
			return await convertApToCcValue(value, sourceField as APGetCustomFieldType, targetField as GetCCCustomField, requestId);
		} else if (sourcePlatform === "cc" && targetPlatform === "ap") {
			return await convertCcToApValue(value, sourceField as GetCCCustomField, targetField as APGetCustomFieldType, requestId);
		} else {
			const error = `Invalid conversion direction: ${sourcePlatform} -> ${targetPlatform}`;
			logError(error, { requestId, sourcePlatform, targetPlatform });
			return { success: false, error };
		}
	} catch (error) {
		const errorMessage = `Value conversion failed: ${String(error)}`;
		logError(errorMessage, {
			requestId,
			sourceFieldId: getFieldId(sourceField),
			targetFieldId: getFieldId(targetField),
			sourceValue: value,
			error,
		});
		return { success: false, error: errorMessage };
	}
}

/**
 * Convert AutoPatient value to CliniCore format
 *
 * Handles conversion from AP field values to CC field values based on
 * field type mappings and value transformation rules.
 *
 * @param value - AP field value to convert
 * @param apField - AP field definition
 * @param ccField - CC field definition
 * @param requestId - Request ID for logging
 * @returns Promise resolving to conversion result
 *
 * @since 1.0.0
 */
async function convertApToCcValue(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): Promise<ValueConversionResult> {
	logDebug("Converting AP to CC value", {
		requestId,
		apFieldId: apField.id,
		apFieldType: apField.dataType,
		ccFieldId: ccField.id,
		ccFieldType: ccField.type,
		sourceValue: value,
	});

	// Handle AP RADIO to CC boolean conversion
	if (apField.dataType === "RADIO" && ccField.type === "boolean") {
		return convertApRadioToCcBoolean(value, apField, requestId);
	}

	// Handle AP RADIO to CC select conversion
	if (apField.dataType === "RADIO" && ccField.type === "select") {
		return convertApRadioToCcSelect(value, apField, ccField, requestId);
	}

	// Handle AP MULTIPLE_OPTIONS to CC select conversion
	if (apField.dataType === "MULTIPLE_OPTIONS" && ccField.type === "select") {
		return convertApMultipleOptionsToCcSelect(value, apField, ccField, requestId);
	}

	// Handle AP TEXT to CC text-based fields
	if (apField.dataType === "TEXT" && isTextBasedCcField(ccField.type)) {
		return convertApTextToCcText(value, requestId);
	}

	// Handle AP TEXT to CC select conversion (when value matches allowed options)
	if (apField.dataType === "TEXT" && ccField.type === "select") {
		return convertApTextToCcSelect(value, apField, ccField, requestId);
	}

	// Fallback: convert to string for text fields, or pass through for compatible types
	if (isTextBasedCcField(ccField.type)) {
		const convertedValue = String(value);
		logWarn("AP→CC fallback conversion to text", {
			requestId,
			apFieldType: apField.dataType,
			ccFieldType: ccField.type,
			originalValue: value,
			convertedValue,
		});
		return { success: true, convertedValue };
	}

	// Unsupported conversion - use enhanced logging
	const error = `Unsupported AP→CC conversion: ${apField.dataType} → ${ccField.type}`;
	logFieldConversionFailure(
		"AP→CC",
		{
			id: apField.id,
			name: apField.name,
			type: apField.dataType,
			fieldKey: apField.fieldKey,
			picklistOptions: apField.picklistOptions,
		},
		{
			id: ccField.id,
			name: ccField.name,
			type: ccField.type,
			allowMultipleValues: ccField.allowMultipleValues,
			allowedValues: ccField.allowedValues,
		},
		value,
		error,
		requestId,
	);
	return { success: false, error };
}

/**
 * Convert CliniCore value to AutoPatient format
 *
 * Handles conversion from CC field values to AP field values based on
 * field type mappings and value transformation rules.
 *
 * @param value - CC field value to convert
 * @param ccField - CC field definition
 * @param apField - AP field definition
 * @param requestId - Request ID for logging
 * @returns Promise resolving to conversion result
 *
 * @since 1.0.0
 */
async function convertCcToApValue(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): Promise<ValueConversionResult> {
	logDebug("Converting CC to AP value", {
		requestId,
		ccFieldId: ccField.id,
		ccFieldType: ccField.type,
		apFieldId: apField.id,
		apFieldType: apField.dataType,
		sourceValue: value,
	});

	// Handle CC boolean to AP RADIO conversion
	if (ccField.type === "boolean" && apField.dataType === "RADIO") {
		return convertCcBooleanToApRadio(value, apField, requestId);
	}

	// Handle CC select to AP RADIO conversion
	if (ccField.type === "select" && apField.dataType === "RADIO") {
		return convertCcSelectToApRadio(value, ccField, apField, requestId);
	}

	// Handle CC select to AP SINGLE_OPTIONS conversion
	if (ccField.type === "select" && apField.dataType === "SINGLE_OPTIONS") {
		return convertCcSelectToApSingleOptions(value, ccField, apField, requestId);
	}

	// Handle CC select to AP MULTIPLE_OPTIONS conversion
	if (ccField.type === "select" && apField.dataType === "MULTIPLE_OPTIONS") {
		return convertCcSelectToApMultipleOptions(value, ccField, apField, requestId);
	}

	// Handle CC text-based fields to AP TEXT
	if (isTextBasedCcField(ccField.type) && apField.dataType === "TEXT") {
		return convertCcTextToApText(value, requestId);
	}

	// Fallback: convert to string for AP TEXT fields
	if (apField.dataType === "TEXT") {
		const convertedValue = String(value);
		logWarn("CC→AP fallback conversion to text", {
			requestId,
			ccFieldType: ccField.type,
			apFieldType: apField.dataType,
			originalValue: value,
			convertedValue,
		});
		return { success: true, convertedValue };
	}

	// Unsupported conversion - use enhanced logging
	const error = `Unsupported CC→AP conversion: ${ccField.type} → ${apField.dataType}`;
	logFieldConversionFailure(
		"CC→AP",
		{
			id: ccField.id,
			name: ccField.name,
			type: ccField.type,
			allowMultipleValues: ccField.allowMultipleValues,
			allowedValues: ccField.allowedValues,
		},
		{
			id: apField.id,
			name: apField.name,
			type: apField.dataType,
			fieldKey: apField.fieldKey,
			picklistOptions: apField.picklistOptions,
		},
		value,
		error,
		requestId,
	);
	return { success: false, error };
}

/**
 * Convert AP RADIO value to CC boolean
 */
function convertApRadioToCcBoolean(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	const stringValue = String(value).toLowerCase().trim();
	
	// Common positive values
	const positiveValues = ["yes", "true", "1", "on", "enabled", "active"];
	// Common negative values  
	const negativeValues = ["no", "false", "0", "off", "disabled", "inactive"];

	if (positiveValues.includes(stringValue)) {
		logDebug("AP RADIO → CC boolean: true", { requestId, originalValue: value });
		return { success: true, convertedValue: true };
	}
	
	if (negativeValues.includes(stringValue)) {
		logDebug("AP RADIO → CC boolean: false", { requestId, originalValue: value });
		return { success: true, convertedValue: false };
	}

	// Fallback: check against AP field options if available
	if (apField.picklistOptions && apField.picklistOptions.length >= 2) {
		const firstOption = apField.picklistOptions[0].toLowerCase();
		const isFirstOption = stringValue === firstOption;
		logDebug("AP RADIO → CC boolean: option-based", {
			requestId,
			originalValue: value,
			convertedValue: isFirstOption,
			firstOption,
		});
		return { success: true, convertedValue: isFirstOption };
	}

	// Default to false for unknown values
	logWarn("AP RADIO → CC boolean: defaulting to false", {
		requestId,
		originalValue: value,
		reason: "Unknown value",
	});
	return { success: true, convertedValue: false };
}

/**
 * Convert CC boolean value to AP RADIO
 */
function convertCcBooleanToApRadio(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	const boolValue = Boolean(value);

	// Use AP field options if available
	if (apField.picklistOptions && apField.picklistOptions.length >= 2) {
		const convertedValue = boolValue ? apField.picklistOptions[0] : apField.picklistOptions[1];
		logDebug("CC boolean → AP RADIO: option-based", {
			requestId,
			originalValue: value,
			convertedValue,
			options: apField.picklistOptions,
		});
		return { success: true, convertedValue };
	}

	// Default Yes/No mapping
	const convertedValue = boolValue ? "Yes" : "No";
	logDebug("CC boolean → AP RADIO: default Yes/No", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Convert AP RADIO value to CC select
 */
function convertApRadioToCcSelect(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): ValueConversionResult {
	const stringValue = String(value);

	// For single-select CC fields, return the value as-is
	if (!ccField.allowMultipleValues) {
		logDebug("AP RADIO → CC select (single)", {
			requestId,
			originalValue: value,
			convertedValue: stringValue,
		});
		return { success: true, convertedValue: stringValue };
	}

	// For multi-select CC fields, return as single-item array
	const convertedValue = [stringValue];
	logDebug("AP RADIO → CC select (multiple)", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Convert CC select value to AP RADIO
 */
function convertCcSelectToApRadio(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	let stringValue: string;

	if (Array.isArray(value)) {
		// Take first value from array
		stringValue = value.length > 0 ? String(value[0]) : "";
		logDebug("CC select → AP RADIO: array to first value", {
			requestId,
			originalValue: value,
			convertedValue: stringValue,
		});
	} else {
		stringValue = String(value);
		logDebug("CC select → AP RADIO: direct conversion", {
			requestId,
			originalValue: value,
			convertedValue: stringValue,
		});
	}

	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC select to AP SINGLE_OPTIONS
 *
 * Converts a CC select field value to an AP SINGLE_OPTIONS field.
 * Handles both single values and arrays (taking the first value for single options).
 * This conversion is used for CC select fields with allowMultipleValues: false
 * that map to AP SINGLE_OPTIONS fields.
 *
 * @param value - CC select field value to convert
 * @param ccField - CC select field definition (for logging context)
 * @param apField - AP SINGLE_OPTIONS field definition (for logging context)
 * @param requestId - Request ID for logging correlation
 * @returns ValueConversionResult with success status and converted value
 *
 * @example
 * ```typescript
 * // CC "health-insurance" (select) = "SVS-GW" → AP "Krankenversicherung" (SINGLE_OPTIONS) = "SVS-GW"
 * const result = convertCcSelectToApSingleOptions("SVS-GW", ccField, apField, "req-123");
 * // result.success = true, result.convertedValue = "SVS-GW"
 * ```
 *
 * @since 1.0.0
 */
function convertCcSelectToApSingleOptions(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	// Handle null/undefined values
	if (value === null || value === undefined) {
		logDebug("CC select → AP SINGLE_OPTIONS: null/undefined value", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			apFieldId: apField.id,
			apFieldName: apField.name,
		});
		return { success: true, convertedValue: null };
	}

	let stringValue: string;

	if (Array.isArray(value)) {
		// Take first value from array (SINGLE_OPTIONS should only have one value)
		stringValue = value.length > 0 ? String(value[0]) : "";
		logDebug("CC select → AP SINGLE_OPTIONS: array to first value", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			apFieldId: apField.id,
			apFieldName: apField.name,
			originalValue: value,
			convertedValue: stringValue,
			arrayLength: value.length,
			conversionType: "select_to_single_options_array",
		});
	} else {
		stringValue = String(value);
		logDebug("CC select → AP SINGLE_OPTIONS: direct conversion", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			apFieldId: apField.id,
			apFieldName: apField.name,
			originalValue: value,
			convertedValue: stringValue,
			conversionType: "select_to_single_options_direct",
		});
	}

	return { success: true, convertedValue: stringValue };
}

/**
 * Convert AP MULTIPLE_OPTIONS to CC select
 */
function convertApMultipleOptionsToCcSelect(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): ValueConversionResult {
	const stringValue = String(value);

	// Split comma-separated values
	const values = stringValue.split(",").map(v => v.trim()).filter(v => v.length > 0);

	if (!ccField.allowMultipleValues && values.length > 1) {
		// CC field doesn't allow multiple values, take first one
		const convertedValue = values[0];
		logWarn("AP MULTIPLE_OPTIONS → CC select: truncated to single value", {
			requestId,
			originalValue: value,
			convertedValue,
			reason: "Target field doesn't allow multiple values",
		});
		return { success: true, convertedValue };
	}

	const convertedValue = ccField.allowMultipleValues ? values : values[0] || "";
	logDebug("AP MULTIPLE_OPTIONS → CC select", {
		requestId,
		originalValue: value,
		convertedValue,
		allowMultiple: ccField.allowMultipleValues,
	});
	return { success: true, convertedValue };
}

/**
 * Convert CC select to AP MULTIPLE_OPTIONS
 */
function convertCcSelectToApMultipleOptions(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): ValueConversionResult {
	let values: string[];

	if (Array.isArray(value)) {
		values = value.map(v => String(v));
	} else {
		values = [String(value)];
	}

	const convertedValue = values.join(",");
	logDebug("CC select → AP MULTIPLE_OPTIONS", {
		requestId,
		originalValue: value,
		convertedValue,
		valueCount: values.length,
	});
	return { success: true, convertedValue };
}

/**
 * Convert AP TEXT to CC text-based field
 */
function convertApTextToCcText(
	value: string | number | boolean | string[] | null,
	requestId: string,
): ValueConversionResult {
	const convertedValue = String(value);
	logDebug("AP TEXT → CC text", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Convert AP TEXT to CC select field (when value matches allowed options)
 *
 * Attempts to convert an AP TEXT field value to a CC select field by checking
 * if the text value exactly matches one of the CC select field's allowed values.
 * Performs case-sensitive string comparison.
 *
 * @param value - AP TEXT field value to convert
 * @param apField - AP field definition (for logging context)
 * @param ccField - CC select field definition with allowedValues
 * @param requestId - Request ID for logging correlation
 * @returns ValueConversionResult with success/failure and converted value
 *
 * @example
 * ```typescript
 * // AP "Gender" (TEXT) = "weiblich" → CC "gender" (select) = "weiblich"
 * const result = convertApTextToCcSelect("weiblich", apField, ccField, "req-123");
 * if (result.success) {
 *   console.log(`Converted: ${result.convertedValue}`);
 * }
 * ```
 *
 * @since 1.0.0
 */
function convertApTextToCcSelect(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): ValueConversionResult {
	// Handle null/undefined values
	if (value === null || value === undefined) {
		logDebug("AP TEXT → CC select: null/undefined value", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
		});
		return { success: true, convertedValue: null };
	}

	// Convert value to string for comparison
	const stringValue = String(value).trim();

	// Handle empty string
	if (stringValue === "") {
		logDebug("AP TEXT → CC select: empty string value", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
		});
		return { success: true, convertedValue: "" };
	}

	// Check if CC field has allowed values
	if (!ccField.allowedValues || ccField.allowedValues.length === 0) {
		logInfo("AP TEXT → CC select: CC field has no allowed values, skipping conversion", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			apFieldValue: stringValue,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			reason: "no_allowed_values",
		});
		return { success: false, error: "CC select field has no allowed values" };
	}

	// Extract allowed value strings for comparison
	const allowedValueStrings = ccField.allowedValues.map(av => av.value);

	// Check for exact match (case-sensitive)
	const matchingValue = allowedValueStrings.find(allowedValue => allowedValue === stringValue);

	if (matchingValue) {
		// Successful match found
		logDebug("AP TEXT → CC select: successful value match", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			apFieldValue: stringValue,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			matchedValue: matchingValue,
			totalAllowedValues: allowedValueStrings.length,
			conversionType: "text_to_select_match",
		});

		return { success: true, convertedValue: matchingValue };
	} else {
		// No match found - skip conversion gracefully
		logInfo("AP TEXT → CC select: no matching allowed value, skipping conversion", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			apFieldValue: stringValue,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			allowedValues: allowedValueStrings.slice(0, 10), // Show first 10 options
			totalAllowedValues: allowedValueStrings.length,
			reason: "no_matching_value",
			conversionType: "text_to_select_skip",
		});

		return {
			success: false,
			error: `AP TEXT value "${stringValue}" does not match any CC select allowed values`
		};
	}
}

/**
 * Convert CC text-based field to AP TEXT
 */
function convertCcTextToApText(
	value: string | number | boolean | string[] | null,
	requestId: string,
): ValueConversionResult {
	const convertedValue = String(value);
	logDebug("CC text → AP TEXT", {
		requestId,
		originalValue: value,
		convertedValue,
	});
	return { success: true, convertedValue };
}

/**
 * Check if CC field type is text-based
 */
function isTextBasedCcField(fieldType: string): boolean {
	const textTypes = ["text", "textarea", "email", "telephone", "url"];
	return textTypes.includes(fieldType.toLowerCase());
}

/**
 * Get field ID from either platform field
 */
function getFieldId(field: APGetCustomFieldType | GetCCCustomField): string | number {
	return 'id' in field ? field.id : (field as GetCCCustomField).id;
}
